using System;
using System.Collections.Generic;
using System.Linq;
using FairyGUI;
using UnityEngine;

public class JigsawPanel : Panel
{
    public JigsawPanel()
    {
        packName = "Jigsaw";
        compName = "JigsawPanel";
    }

    private OperationLayer oprationLayer;
    private GList listStorage;
    private GComponent oprationComponent;
    private int imageIndex;
    private GComponent pieceContainer;  // 专门用于管理piece和thickness渲染的容器

    // 组管理
    private List<JigsawGroup> groups = new List<JigsawGroup>();
    protected override void DoInitialize()
    {
        UIObjectFactory.SetPackageItemExtension($"ui://{packName}/JigsawPiece", () => { return new JigsawPiece(); });

        oprationComponent = contentPane.GetChild("oprationLayer").asCom;
        listStorage = contentPane.GetChild("listStorage").asList;

        // 创建专门的渲染容器来管理所有piece和thickness
        pieceContainer = new GComponent();
        pieceContainer.name = "PieceContainer";
        pieceContainer.SetSize(contentPane.width, contentPane.height);
        pieceContainer.fairyBatching = false;  // 禁用批处理，避免自动重排序覆盖手动设置的sortingOrder
        contentPane.AddChild(pieceContainer);
        
        // 创建OperationLayer
        oprationLayer = new OperationLayer(oprationComponent, this, pieceContainer);
        
        // 新系统中通过层级索引管理显示顺序
        contentPane.fairyBatching = false;

        listStorage.itemRenderer = UpdateStoragePiece;

        SetData();
    }

    public void SetData()
    {
        imageIndex = 1;
        FUILoader.LoadPackage($"Z_Image_{imageIndex}",()=>
        {
            if(contentPane == null || contentPane.isDisposed) return;
            listStorage.numItems = 6 * 8;
        });
    }

    private void UpdateStoragePiece(int index, GObject item)
    {
        var piece = item as JigsawPiece;
        piece.SetPiece(imageIndex, index);

        // 设置父面板引用，用于拖拽处理
        piece.SetParentPanel(this);
        
        // 设置存储标识
        piece.SetInStorage(true);
    }

    /// <summary>
    /// 设置网格显示状态
    /// </summary>
    /// <param name="visible">是否显示网格</param>
    public void SetGridVisible(bool visible)
    {
        oprationLayer?.SetGridVisible(visible);
    }


    /// <summary>
    /// 获取操作层的网格坐标
    /// </summary>
    /// <param name="localPosition">本地坐标</param>
    /// <returns>网格坐标</returns>
    public Vector2Int GetGridPosition(Vector2 localPosition)
    {
        return oprationLayer?.GetGridPosition(localPosition) ?? Vector2Int.zero;
    }

    /// <summary>
    /// 获取网格坐标对应的本地位置
    /// </summary>
    /// <param name="gridPosition">网格坐标</param>
    /// <returns>本地坐标</returns>
    public Vector2 GetLocalPosition(Vector2Int gridPosition)
    {
        return oprationLayer?.GetLocalPosition(gridPosition) ?? Vector2.zero;
    }

    /// <summary>
    /// 将全局坐标转换为操作层的本地坐标
    /// </summary>
    /// <param name="globalPosition">全局坐标</param>
    /// <returns>操作层本地坐标</returns>
    public Vector2 GlobalToOperationLayerLocal(Vector2 globalPosition)
    {
        return oprationComponent.GlobalToLocal(globalPosition);
    }

    /// <summary>
    /// 将操作层本地坐标转换为全局坐标
    /// </summary>
    /// <param name="localPosition">操作层本地坐标</param>
    /// <returns>全局坐标</returns>
    public Vector2 OperationLayerLocalToGlobal(Vector2 localPosition)
    {
        return oprationComponent.LocalToGlobal(localPosition);
    }

    /// <summary>
    /// 检查位置是否在操作层范围内
    /// </summary>
    /// <param name="operationLayerLocalPos">操作层本地坐标</param>
    /// <returns>是否在范围内</returns>
    public bool IsPositionInOperationLayer(Vector2 operationLayerLocalPos)
    {
        return operationLayerLocalPos.x >= 0 && operationLayerLocalPos.y >= 0 &&
               operationLayerLocalPos.x <= oprationComponent.width &&
               operationLayerLocalPos.y <= oprationComponent.height;
    }

    /// <summary>
    /// 获取操作层对象
    /// </summary>
    /// <returns>操作层对象</returns>
    public OperationLayer GetOperationLayer()
    {
        return oprationLayer;
    }

    
    /// <summary>
    /// 获取渲染容器
    /// </summary>
    /// <returns>渲染容器</returns>
    public GComponent GetPieceContainer()
    {
        return pieceContainer;
    }

    /// <summary>
    /// 检查两个拼块是否同时满足原图相邻和操作区域格子相邻，且相对位置关系正确
    /// </summary>
    /// <param name="piece1">拼块1</param>
    /// <param name="piece2">拼块2</param>
    /// <returns>是否同时满足相邻条件和正确的相对位置关系</returns>
    private bool IsAdjacentInBothOriginalAndOperation(JigsawPiece piece1, JigsawPiece piece2)
    {
        if (piece1 == null || piece2 == null) return false;

        // 首先检查原图中是否相邻
        if (!piece1.IsAdjacentTo(piece2))
        {
            Debug.Log($"拼块 {piece1.pieceIndex} 和 {piece2.pieceIndex} 在原图中不相邻");
            return false;
        }

        // 获取原图中的相对位置关系
        Vector2Int piece1OriginalPos = piece1.GetOriginalGridPosition();
        Vector2Int piece2OriginalPos = piece2.GetOriginalGridPosition();
        Vector2Int originalRelativePos = piece2OriginalPos - piece1OriginalPos;

        // 获取操作区域中的位置
        Vector2 piece1Center = piece1.LocalToGlobal(new Vector2(piece1.width * 0.5f, piece1.height * 0.5f));
        Vector2 piece2Center = piece2.LocalToGlobal(new Vector2(piece2.width * 0.5f, piece2.height * 0.5f));

        Vector2 piece1OperationPos = GlobalToOperationLayerLocal(piece1Center);
        Vector2 piece2OperationPos = GlobalToOperationLayerLocal(piece2Center);

        Vector2Int piece1GridPos = GetGridPosition(piece1OperationPos);
        Vector2Int piece2GridPos = GetGridPosition(piece2OperationPos);
        Vector2Int operationRelativePos = piece2GridPos - piece1GridPos;

        Debug.Log($"拼块 {piece1.pieceIndex} 和 {piece2.pieceIndex}:");
        Debug.Log($"  原图位置: {piece1OriginalPos} -> {piece2OriginalPos}, 相对位置: {originalRelativePos}");
        Debug.Log($"  操作层位置: {piece1GridPos} -> {piece2GridPos}, 相对位置: {operationRelativePos}");

        // 检查相对位置关系是否一致
        bool isAdjacent = originalRelativePos == operationRelativePos;
        Debug.Log($"  相对位置关系一致: {isAdjacent}");

        return isAdjacent;
    }

    /// <summary>
    /// 检查并创建拼块组
    /// </summary>
    /// <param name="newPiece">新放置的拼块</param>
    public void CheckAndCreateGroups(JigsawPiece newPiece)
    {
        if (newPiece == null) return;

        Debug.Log($"CheckAndCreateGroups: 检查拼块 {newPiece.pieceIndex} 的组合");

        // 获取操作层中的所有拼块
        var operationPieces = GetPiecesInOperationLayer();
        Debug.Log($"操作层中共有 {operationPieces.Count} 个拼块");

        // 查找与新拼块相邻的拼块（需要同时满足原图相邻和操作区域格子相邻）
        var adjacentPieces = new List<JigsawPiece>();
        foreach (var piece in operationPieces)
        {
            if (piece != newPiece && IsAdjacentInBothOriginalAndOperation(newPiece, piece))
            {
                adjacentPieces.Add(piece);
                Debug.Log($"找到相邻拼块: {piece.pieceIndex}，当前组: {piece.GetGroup()?.GroupId}");
            }
        }

        Debug.Log($"找到 {adjacentPieces.Count} 个相邻拼块");

        if (adjacentPieces.Count == 0)
        {
            // 没有相邻拼块，不需要创建组
            Debug.Log("没有相邻拼块，不创建组");
            return;
        }

        // 检查相邻拼块是否已经属于某个组
        var existingGroups = new HashSet<JigsawGroup>();
        foreach (var piece in adjacentPieces)
        {
            var group = piece.GetGroup();
            if (group != null)
            {
                existingGroups.Add(group);
            }
        }

        Debug.Log($"找到 {existingGroups.Count} 个现有组");

        if (existingGroups.Count == 0)
        {
            // 没有现有组，创建新组
            Debug.Log("创建新组");
            var newGroup = new JigsawGroup(this);
            newGroup.AddPiece(newPiece);
            foreach (var piece in adjacentPieces)
            {
                newGroup.AddPiece(piece);
            }
            groups.Add(newGroup);
            Debug.Log($"新组创建完成，组ID: {newGroup.GroupId}，包含 {newGroup.Count} 个拼块");
        }
        else if (existingGroups.Count == 1)
        {
            // 有一个现有组，将新拼块和其他相邻拼块加入该组
            var existingGroup = existingGroups.First();
            Debug.Log($"将拼块加入现有组 {existingGroup.GroupId}");
            existingGroup.AddPiece(newPiece);
            foreach (var piece in adjacentPieces)
            {
                if (piece.GetGroup() == null)
                {
                    existingGroup.AddPiece(piece);
                }
            }
            Debug.Log($"组 {existingGroup.GroupId} 现在包含 {existingGroup.Count} 个拼块");
        }
        else
        {
            // 有多个现有组，需要合并
            Debug.Log($"需要合并 {existingGroups.Count} 个组");
            var primaryGroup = existingGroups.First();
            Debug.Log($"主组: {primaryGroup.GroupId}");

            // 检查组合并的条件
            var groupsToMerge = new List<JigsawGroup>();
            foreach (var group in existingGroups.Skip(1))
            {
                Debug.Log($"检查组 {group.GroupId} 是否可以与主组 {primaryGroup.GroupId} 合并");
                if (primaryGroup.CanMergeWith(group))
                {
                    groupsToMerge.Add(group);
                    Debug.Log($"组 {group.GroupId} 可以合并");
                }
                else
                {
                    Debug.Log($"组 {group.GroupId} 不能合并");
                }
            }

            primaryGroup.AddPiece(newPiece);

            // 合并其他组到主组
            var groupsToRemove = new List<JigsawGroup>();
            foreach (var group in groupsToMerge)
            {
                Debug.Log($"合并组 {group.GroupId} 到主组 {primaryGroup.GroupId}");
                if (primaryGroup.MergeWith(group))
                {
                    groupsToRemove.Add(group);
                    Debug.Log($"组 {group.GroupId} 合并成功");
                }
                else
                {
                    Debug.Log($"组 {group.GroupId} 合并失败");
                }
            }

            // 移除已合并的组
            foreach (var group in groupsToRemove)
            {
                groups.Remove(group);
                group.Dispose();
            }

            // 添加没有组的相邻拼块
            foreach (var piece in adjacentPieces)
            {
                if (piece.GetGroup() == null)
                {
                    primaryGroup.AddPiece(piece);
                }
            }

            Debug.Log($"合并完成，主组 {primaryGroup.GroupId} 现在包含 {primaryGroup.Count} 个拼块");
        }

        // 更新所有组合的视觉状态，确保达到阈值的组合显示正确的锁定效果
        UpdateAllGroupsVisualState();
    }

    /// <summary>
    /// 获取操作层中的所有拼块
    /// </summary>
    /// <returns>拼块列表</returns>
    public List<JigsawPiece> GetPiecesInOperationLayer()
    {
        var operationLayer = GetOperationLayer();
        var layerManager = operationLayer.GetLayerManager();
        
        var pieces = layerManager.GetAllRegisteredPieces();
        return pieces;
    }
    
    /// <summary>
    /// 更新所有组合的视觉状态
    /// </summary>
    public void UpdateAllGroupsVisualState()
    {
        foreach (var group in groups)
        {
            // 触发组合视觉效果更新，这会检查是否达到阈值并应用相应的视觉效果
            group.UpdateGroupVisual();
        }
    }
}
