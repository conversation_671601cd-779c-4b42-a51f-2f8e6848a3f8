using System.Collections.Generic;
using UnityEngine;
using FairyGUI;

/// <summary>
/// 拼块组管理类，用于管理相邻拼块的成组功能
/// </summary>
public class JigsawGroup
{
    private static int nextGroupId = 1; // 静态计数器，用于生成唯一ID

    private int groupId; // 组的唯一标识符
    private List<JigsawPiece> pieces = new List<JigsawPiece>();
    private JigsawPanel parentPanel;
    private bool isDragging = false;
    private Vector2 dragStartOffset;

    // 拼图网格配置
    private const int GRID_COLUMNS = 6;
    private const int GRID_ROWS = 8;

    // 组合锁定阈值：当组合达到此数量且所有拼块都在正确位置时，组合将被锁定无法拖动
    private const int LOCK_THRESHOLD = 10;

    // 预定义的高亮颜色数组
    private static readonly Color[] GROUP_HIGHLIGHT_COLORS = new Color[]
    {
        new Color(1.0f, 1.0f, 0.0f, 1.0f),    // 黄色
        new Color(0.0f, 1.0f, 1.0f, 1.0f),    // 青色
        new Color(1.0f, 0.5f, 0.0f, 1.0f),    // 橙色
        new Color(1.0f, 0.0f, 1.0f, 1.0f),    // 洋红色
        new Color(0.5f, 1.0f, 0.0f, 1.0f),    // 浅绿色
        new Color(0.0f, 0.5f, 1.0f, 1.0f),    // 浅蓝色
        new Color(1.0f, 0.0f, 0.5f, 1.0f),    // 粉红色
        new Color(0.5f, 0.0f, 1.0f, 1.0f),    // 紫色
        new Color(1.0f, 1.0f, 0.5f, 1.0f),    // 浅黄色
        new Color(0.5f, 1.0f, 1.0f, 1.0f),    // 浅青色
    };

    public JigsawGroup(JigsawPanel panel)
    {
        parentPanel = panel;
        groupId = nextGroupId++;
    }
    
    /// <summary>
    /// 获取组中的拼块数量
    /// </summary>
    public int Count => pieces.Count;

    /// <summary>
    /// 获取组中的所有拼块
    /// </summary>
    public List<JigsawPiece> Pieces => new List<JigsawPiece>(pieces);

    /// <summary>
    /// 获取组的唯一标识符
    /// </summary>
    public int GroupId => groupId;

    /// <summary>
    /// 获取组的高亮颜色
    /// </summary>
    /// <returns>组的高亮颜色</returns>
    public Color GetGroupHighlightColor()
    {
        // 使用组ID对颜色数组长度取模，确保每个组都有唯一的颜色
        int colorIndex = (groupId - 1) % GROUP_HIGHLIGHT_COLORS.Length;
        Color selectedColor = GROUP_HIGHLIGHT_COLORS[colorIndex];

        return selectedColor;
    }

    /// <summary>
    /// 检查组合是否可以拖动（如果组合数量达到阈值且所有拼块都在原图正确位置则不能拖动）
    /// </summary>
    /// <returns>是否可以拖动</returns>
    public bool CanDrag()
    {
        // 如果组合数量未达到锁定阈值，可以拖动
        if (pieces.Count < LOCK_THRESHOLD)
            return true;

        // 检查所有拼块是否都在原图正确位置
        return !AreAllPiecesInCorrectPosition();
    }

    /// <summary>
    /// 检查组中所有拼块是否都在原图的正确位置
    /// </summary>
    /// <returns>是否所有拼块都在正确位置</returns>
    private bool AreAllPiecesInCorrectPosition()
    {
        if (parentPanel == null) return false;

        foreach (var piece in pieces)
        {
            // 获取拼块在原图中的网格位置
            Vector2Int originalGridPos = GetGridPosition(piece.pieceIndex);

            // 获取拼块在操作层中的网格位置
            Vector2 pieceCenter = piece.LocalToGlobal(new Vector2(piece.width * 0.5f, piece.height * 0.5f));
            Vector2 operationLayerPos = parentPanel.GlobalToOperationLayerLocal(pieceCenter);
            Vector2Int currentGridPos = parentPanel.GetGridPosition(operationLayerPos);

            // 如果任何一个拼块不在正确位置，返回false
            if (originalGridPos != currentGridPos)
                return false;
        }

        return true;
    }

    /// <summary>
    /// 添加拼块到组中
    /// </summary>
    /// <param name="piece">要添加的拼块</param>
    /// <returns>是否成功添加</returns>
    public bool AddPiece(JigsawPiece piece)
    {
        if (piece == null || pieces.Contains(piece))
            return false;

        // 检查是否与组中现有拼块相邻
        if (pieces.Count > 0 && !IsAdjacentToGroup(piece))
            return false;

        // 如果拼块已经属于另一个组，先从那个组中移除
        var currentGroup = piece.GetGroup();
        if (currentGroup != null && currentGroup != this)
        {
            Debug.Log($"拼块 {piece.pieceIndex} 从组 {currentGroup.GroupId} 移动到组 {groupId}");
            currentGroup.RemovePiece(piece);
        }

        pieces.Add(piece);
        piece.SetGroup(this);

        // 更新视觉效果
        UpdateGroupVisual();

        return true;
    }
    
    /// <summary>
    /// 从组中移除拼块
    /// </summary>
    /// <param name="piece">要移除的拼块</param>
    /// <returns>是否成功移除</returns>
    public bool RemovePiece(JigsawPiece piece)
    {
        if (piece == null || !pieces.Contains(piece))
            return false;

        Debug.Log($"从组 {groupId} 中移除拼块 {piece.pieceIndex}");

        pieces.Remove(piece);
        piece.SetGroup(null);

        // 如果组中还有拼块，更新视觉效果
        if (pieces.Count > 0)
        {
            UpdateGroupVisual();
        }
        else
        {
            Debug.Log($"组 {groupId} 已变空");
        }

        return true;
    }
    
    /// <summary>
    /// 检查拼块是否与组中的拼块相邻（需要同时满足原图相邻和操作区域格子相邻，且相对位置关系正确）
    /// </summary>
    /// <param name="piece">要检查的拼块</param>
    /// <returns>是否相邻</returns>
    private bool IsAdjacentToGroup(JigsawPiece piece)
    {
        foreach (var groupPiece in pieces)
        {
            // 检查原图中是否相邻
            Vector2Int pieceOriginalPos = GetGridPosition(piece.pieceIndex);
            Vector2Int groupPieceOriginalPos = GetGridPosition(groupPiece.pieceIndex);
            if (!IsAdjacent(pieceOriginalPos, groupPieceOriginalPos))
                continue;

            // 获取原图中的相对位置关系
            Vector2Int originalRelativePos = groupPieceOriginalPos - pieceOriginalPos;

            // 检查在操作区域格子中的相对位置关系
            if (parentPanel != null)
            {
                Vector2 pieceCenter = piece.LocalToGlobal(new Vector2(piece.width * 0.5f, piece.height * 0.5f));
                Vector2 groupPieceCenter = groupPiece.LocalToGlobal(new Vector2(groupPiece.width * 0.5f, groupPiece.height * 0.5f));

                Vector2 pieceOperationPos = parentPanel.GlobalToOperationLayerLocal(pieceCenter);
                Vector2 groupPieceOperationPos = parentPanel.GlobalToOperationLayerLocal(groupPieceCenter);

                Vector2Int pieceOperationGridPos = parentPanel.GetGridPosition(pieceOperationPos);
                Vector2Int groupPieceOperationGridPos = parentPanel.GetGridPosition(groupPieceOperationPos);
                Vector2Int operationRelativePos = groupPieceOperationGridPos - pieceOperationGridPos;

                // 检查相对位置关系是否一致
                if (originalRelativePos == operationRelativePos)
                    return true;
            }
            else
            {
                // 如果没有parentPanel引用，只检查原图相邻性（向后兼容）
                return true;
            }
        }

        return false;
    }
    
    /// <summary>
    /// 根据拼块索引计算在6x8网格中的位置
    /// </summary>
    /// <param name="pieceIndex">拼块索引</param>
    /// <returns>网格位置</returns>
    public static Vector2Int GetGridPosition(int pieceIndex)
    {
        int x = pieceIndex % GRID_COLUMNS;
        int y = pieceIndex / GRID_COLUMNS;
        return new Vector2Int(x, y);
    }
    
    /// <summary>
    /// 检查两个网格位置是否相邻（水平或垂直相邻）
    /// </summary>
    /// <param name="pos1">位置1</param>
    /// <param name="pos2">位置2</param>
    /// <returns>是否相邻</returns>
    public static bool IsAdjacent(Vector2Int pos1, Vector2Int pos2)
    {
        int deltaX = Mathf.Abs(pos1.x - pos2.x);
        int deltaY = Mathf.Abs(pos1.y - pos2.y);
        
        // 相邻的条件：一个方向差值为1，另一个方向差值为0
        return (deltaX == 1 && deltaY == 0) || (deltaX == 0 && deltaY == 1);
    }
    
    /// <summary>
    /// 开始组拖拽
    /// </summary>
    /// <param name="draggedPiece">被拖拽的拼块</param>
    /// <param name="startPosition">拖拽开始位置</param>
    public void StartGroupDrag(JigsawPiece draggedPiece, Vector2 startPosition)
    {
        if (!pieces.Contains(draggedPiece))
            return;
            
        isDragging = true;
        dragStartOffset = startPosition - draggedPiece.xy;
        
        // 记录所有拼块的初始位置
        foreach (var piece in pieces)
        {
            if (piece != draggedPiece)
            {
                piece.SetGroupDragOffset(piece.xy - draggedPiece.xy);
            }
        }
    }
    
    /// <summary>
    /// 更新组拖拽
    /// </summary>
    /// <param name="draggedPiece">被拖拽的拼块</param>
    /// <param name="currentPosition">当前位置</param>
    public void UpdateGroupDrag(JigsawPiece draggedPiece, Vector2 currentPosition)
    {
        if (!isDragging || !pieces.Contains(draggedPiece))
            return;
            
        Vector2 targetPosition = currentPosition - dragStartOffset;
        
        // 更新所有其他拼块的位置
        foreach (var piece in pieces)
        {
            if (piece != draggedPiece)
            {
                Vector2 pieceTargetPos = targetPosition + piece.GetGroupDragOffset();
                piece.SetXY(pieceTargetPos.x, pieceTargetPos.y);

                // 同步更新thickness位置
                if (parentPanel != null)
                {
                    var operationLayer = parentPanel.GetOperationLayer();
                    operationLayer?.OnPiecePositionChanged(piece);
                }
            }
        }
    }
    
    /// <summary>
    /// 结束组拖拽
    /// </summary>
    public void EndGroupDrag()
    {
        isDragging = false;
        
        // 清除所有拼块的拖拽偏移
        foreach (var piece in pieces)
        {
            piece.ClearGroupDragOffset();
        }
    }
    
    /// <summary>
    /// 更新组的视觉效果
    /// </summary>
    public void UpdateGroupVisual()
    {
        // 检查是否为不可拖动的组合（达到10个且在正确位置）
        bool isLocked = !CanDrag();

        // 获取组的高亮颜色
        Color groupColor = GetGroupHighlightColor();

        // 为组中的拼块添加视觉标识
        foreach (var piece in pieces)
        {
            // 如果组合被锁定，可以添加特殊的视觉效果
            if (isLocked)
            {
                piece.SetLockedHighlight(true);
            }
            else
            {
                // 使用组的专属颜色进行高亮
                piece.SetGroupHighlight(true, groupColor);
            }
        }
    }
    
    /// <summary>
    /// 清除组的视觉效果
    /// </summary>
    public void ClearGroupVisual()
    {
        foreach (var piece in pieces)
        {
            piece.SetGroupHighlight(false);
        }
    }
    
    /// <summary>
    /// 销毁组
    /// </summary>
    public void Dispose()
    {
        ClearGroupVisual();
        
        // 只清除仍然属于当前组的拼块的组引用
        foreach (var piece in pieces)
        {
            if (piece.GetGroup() == this)
            {
                piece.SetGroup(null);
            }
        }
        
        pieces.Clear();
    }
    
    /// <summary>
    /// 检查是否可以与另一个组合并（需要同时满足原图相邻和操作区域格子相邻，且相对位置关系正确）
    /// </summary>
    /// <param name="otherGroup">另一个组</param>
    /// <returns>是否可以合并</returns>
    public bool CanMergeWith(JigsawGroup otherGroup)
    {
        if (otherGroup == null || otherGroup == this)
        {
            Debug.Log($"组 {groupId}: 无法与空组或自身合并");
            return false;
        }

        Debug.Log($"检查组 {groupId} 是否可以与组 {otherGroup.groupId} 合并");

        // 检查两个组之间是否有相邻的拼块
        foreach (var piece1 in pieces)
        {
            foreach (var piece2 in otherGroup.pieces)
            {
                // 检查原图中是否相邻
                Vector2Int pos1 = GetGridPosition(piece1.pieceIndex);
                Vector2Int pos2 = GetGridPosition(piece2.pieceIndex);
                if (!IsAdjacent(pos1, pos2))
                    continue;

                Debug.Log($"  拼块 {piece1.pieceIndex} 和 {piece2.pieceIndex} 在原图中相邻");

                // 获取原图中的相对位置关系
                Vector2Int originalRelativePos = pos2 - pos1;

                // 检查在操作区域格子中的相对位置关系
                if (parentPanel != null)
                {
                    Vector2 piece1Center = piece1.LocalToGlobal(new Vector2(piece1.width * 0.5f, piece1.height * 0.5f));
                    Vector2 piece2Center = piece2.LocalToGlobal(new Vector2(piece2.width * 0.5f, piece2.height * 0.5f));

                    Vector2 piece1OperationPos = parentPanel.GlobalToOperationLayerLocal(piece1Center);
                    Vector2 piece2OperationPos = parentPanel.GlobalToOperationLayerLocal(piece2Center);

                    Vector2Int piece1OperationGridPos = parentPanel.GetGridPosition(piece1OperationPos);
                    Vector2Int piece2OperationGridPos = parentPanel.GetGridPosition(piece2OperationPos);
                    Vector2Int operationRelativePos = piece2OperationGridPos - piece1OperationGridPos;

                    Debug.Log($"    原图相对位置: {originalRelativePos}, 操作层相对位置: {operationRelativePos}");

                    // 检查相对位置关系是否一致
                    if (originalRelativePos == operationRelativePos)
                    {
                        Debug.Log($"  组 {groupId} 可以与组 {otherGroup.groupId} 合并");
                        return true;
                    }
                    else
                    {
                        Debug.Log($"    相对位置关系不一致，不能合并");
                    }
                }
                else
                {
                    // 如果没有parentPanel引用，只检查原图相邻性（向后兼容）
                    Debug.Log($"  没有parentPanel引用，基于原图相邻性允许合并");
                    return true;
                }
            }
        }

        Debug.Log($"组 {groupId} 不能与组 {otherGroup.groupId} 合并");
        return false;
    }
    
    /// <summary>
    /// 与另一个组合并
    /// </summary>
    /// <param name="otherGroup">要合并的组</param>
    /// <returns>是否成功合并</returns>
    public bool MergeWith(JigsawGroup otherGroup)
    {
        if (!CanMergeWith(otherGroup))
            return false;
            
        // 将另一个组的所有拼块添加到当前组
        var otherPieces = new List<JigsawPiece>(otherGroup.pieces);
        foreach (var piece in otherPieces)
        {
            // 直接从另一个组中移除，不触发RemovePiece的其他逻辑
            otherGroup.pieces.Remove(piece);
            // 添加到当前组
            pieces.Add(piece);
            piece.SetGroup(this);
        }
        
        // 更新视觉效果
        UpdateGroupVisual();
        
        return true;
    }
}
